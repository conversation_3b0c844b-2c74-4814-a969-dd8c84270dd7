# Conversation Member Unread Query Optimization

## Overview
This document outlines the optimization performed on the `getMessageUnreadByConversation` method in the `ConversationMemberService` to improve query performance, particularly for handling last read time comparisons.

## Original Query Issues

### Performance Problems
1. **Expensive LEFT JOIN**: The original query performed a LEFT JOIN between `conversation_members` and `messages` tables, which could be very costly with large message datasets
2. **Inefficient WHERE clauses**: Date comparisons and status filtering happened after the JOIN operation
3. **Missing indexes**: No composite indexes optimized for the specific query pattern
4. **Redundant GROUP BY**: Grouping by `last_read_time` when it's already unique per conversation-user pair

### Original Query Structure
```sql
SELECT 
  conversation_members.conversation_id,
  COUNT(messages.id) AS total,
  MAX(conversation_members.last_read_time) AS last_read_time
FROM conversation_members
LEFT JOIN messages ON messages.conversation_id = conversation_members.conversation_id
WHERE conversation_members.conversation_id IN (...)
  AND conversation_members.user_id = ?
  AND (conversation_members.last_read_time < messages.created_at OR conversation_members.last_read_time IS NULL)
  AND (messages.status = ? OR (messages.status = ? AND conversation_members.is_owner = ?))
GROUP BY conversation_members.conversation_id, conversation_members.last_read_time
ORDER BY conversation_members.last_read_time DESC
```

## Optimization Strategy

### 1. Query Structure Optimization
- **CTE (Common Table Expression)**: Used CTEs to break down the query into logical steps
- **Eliminated redundant GROUP BY**: Removed unnecessary grouping by `last_read_time`
- **Improved JOIN conditions**: Made JOIN conditions more selective

### 2. Database Index Optimization
Created composite indexes to support the optimized query:

```sql
-- For conversation_members table
CREATE INDEX idx_conversation_members_user_conversation_deleted 
ON conversation_members (user_id, conversation_id, deleted_at);

CREATE INDEX idx_conversation_members_last_read_time_conversation 
ON conversation_members (last_read_time, conversation_id);

-- For messages table
CREATE INDEX idx_messages_conversation_created_status_deleted 
ON messages (conversation_id, created_at, status, deleted_at);

CREATE INDEX idx_messages_created_at 
ON messages (created_at);
```

## Optimized Query Structure

### New Implementation
```sql
WITH conversation_member_data AS (
  SELECT 
    cm.conversation_id,
    cm.last_read_time,
    cm.is_owner
  FROM conversation_members cm
  WHERE cm.conversation_id = ANY($1::uuid[])
    AND cm.user_id = $2
    AND cm.deleted_at IS NULL
),
unread_counts AS (
  SELECT 
    cmd.conversation_id,
    cmd.last_read_time,
    COUNT(m.id) as total
  FROM conversation_member_data cmd
  LEFT JOIN messages m ON (
    m.conversation_id = cmd.conversation_id
    AND m.deleted_at IS NULL
    AND (cmd.last_read_time IS NULL OR m.created_at > cmd.last_read_time)
    AND (
      m.status = $3
      OR (m.status = $4 AND cmd.is_owner = $5)
    )
  )
  GROUP BY cmd.conversation_id, cmd.last_read_time
)
SELECT 
  conversation_id,
  last_read_time,
  COALESCE(total, 0) as total
FROM unread_counts
ORDER BY last_read_time DESC NULLS LAST
```

## Performance Improvements

### Expected Benefits
1. **Reduced JOIN complexity**: CTE approach makes the query more readable and allows the database optimizer to better plan execution
2. **Index utilization**: Composite indexes ensure optimal query execution paths
3. **Better memory usage**: Eliminates unnecessary data processing in intermediate steps
4. **Improved scalability**: Query performance scales better with increasing message volume

### Key Optimizations
1. **Early filtering**: Filter conversation members first before joining with messages
2. **Selective JOIN**: JOIN conditions are more selective, reducing the working dataset
3. **Proper indexing**: Composite indexes support the exact query pattern
4. **NULL handling**: Explicit NULL handling for last_read_time comparisons

## Migration Requirements

### Database Migration
Run the migration file: `1741200000000-optimize-conversation-member-unread-query-indexes.ts`

```bash
npm run migration:run
```

### Backward Compatibility
The optimized method maintains the same interface and return structure, ensuring no breaking changes for existing code.

## Testing Recommendations

### Performance Testing
1. **Benchmark with large datasets**: Test with conversations containing 10k+ messages
2. **Concurrent load testing**: Verify performance under multiple simultaneous requests
3. **Index effectiveness**: Use EXPLAIN ANALYZE to verify index usage

### Functional Testing
1. **Edge cases**: Test with NULL last_read_time values
2. **Owner permissions**: Verify drafted message visibility for owners
3. **Multiple conversations**: Test with various conversation ID arrays

## Monitoring

### Key Metrics to Monitor
1. **Query execution time**: Should see significant reduction in execution time
2. **Database CPU usage**: Lower CPU utilization during peak usage
3. **Index hit ratio**: Ensure new indexes are being utilized effectively
4. **Memory usage**: Monitor for any memory usage changes

## Future Considerations

### Additional Optimizations
1. **Caching layer**: Consider implementing Redis caching for frequently accessed data
2. **Read replicas**: Use read replicas for this read-heavy operation
3. **Pagination**: For very large conversation lists, consider implementing pagination
4. **Materialized views**: For extremely high-volume scenarios, consider materialized views
