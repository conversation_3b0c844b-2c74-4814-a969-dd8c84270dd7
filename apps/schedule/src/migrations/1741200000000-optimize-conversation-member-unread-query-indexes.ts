import { MigrationInterface, QueryRunner, TableIndex } from 'typeorm';

export class OptimizeConversationMemberUnreadQueryIndexes1741200000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Critical composite index for conversation_members - optimized for large datasets
    // This index supports the WHERE clause filtering (user_id, conversation_id, deleted_at)
    await queryRunner.createIndex(
      'conversation_members',
      new TableIndex({
        name: 'idx_cm_user_conversation_deleted_lastread',
        columnNames: ['user_id', 'conversation_id', 'deleted_at', 'last_read_time'],
      }),
    );

    // Optimized composite index for messages table - covers JOIN conditions efficiently
    // Order matters: conversation_id (JOIN), deleted_at (filter), created_at (comparison), status (filter)
    await queryRunner.createIndex(
      'messages',
      new TableIndex({
        name: 'idx_messages_conversation_deleted_created_status',
        columnNames: ['conversation_id', 'deleted_at', 'created_at', 'status'],
      }),
    );

    // Additional covering index for messages to avoid table lookups
    // This index can serve the entire JOIN condition without accessing the main table
    await queryRunner.createIndex(
      'messages',
      new TableIndex({
        name: 'idx_messages_covering_unread_query',
        columnNames: ['conversation_id', 'created_at', 'status', 'deleted_at', 'id'],
      }),
    );

    // Partial index for messages with only non-deleted records (most common case)
    // This significantly reduces index size and improves performance
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY idx_messages_conversation_created_active
      ON messages (conversation_id, created_at, status)
      WHERE deleted_at IS NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(
      'conversation_members',
      'idx_cm_user_conversation_deleted_lastread',
    );
    await queryRunner.dropIndex(
      'messages',
      'idx_messages_conversation_deleted_created_status',
    );
    await queryRunner.dropIndex(
      'messages',
      'idx_messages_covering_unread_query',
    );
    await queryRunner.query(`
      DROP INDEX CONCURRENTLY IF EXISTS idx_messages_conversation_created_active
    `);
  }
}
