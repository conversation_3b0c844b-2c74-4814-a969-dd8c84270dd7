import { MigrationInterface, QueryRunner, TableIndex } from 'typeorm';

export class OptimizeConversationMemberUnreadQueryIndexes1741200000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Composite index for conversation_members table to optimize the main query
    await queryRunner.createIndex(
      'conversation_members',
      new TableIndex({
        name: 'idx_conversation_members_user_conversation_deleted',
        columnNames: ['user_id', 'conversation_id', 'deleted_at'],
      }),
    );

    // Composite index for conversation_members to optimize last_read_time ordering
    await queryRunner.createIndex(
      'conversation_members',
      new TableIndex({
        name: 'idx_conversation_members_last_read_time_conversation',
        columnNames: ['last_read_time', 'conversation_id'],
      }),
    );

    // Composite index for messages table to optimize the subquery
    await queryRunner.createIndex(
      'messages',
      new TableIndex({
        name: 'idx_messages_conversation_created_status_deleted',
        columnNames: ['conversation_id', 'created_at', 'status', 'deleted_at'],
      }),
    );

    // Additional index for messages created_at to optimize date comparisons
    await queryRunner.createIndex(
      'messages',
      new TableIndex({
        name: 'idx_messages_created_at',
        columnNames: ['created_at'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(
      'conversation_members',
      'idx_conversation_members_user_conversation_deleted',
    );
    await queryRunner.dropIndex(
      'conversation_members',
      'idx_conversation_members_last_read_time_conversation',
    );
    await queryRunner.dropIndex(
      'messages',
      'idx_messages_conversation_created_status_deleted',
    );
    await queryRunner.dropIndex('messages', 'idx_messages_created_at');
  }
}
