import { Test, TestingModule } from '@nestjs/testing';
import { ConversationMemberService } from '../conversation-member.service';
import { ConversationMemberRepositoryInterface } from '@app/shared/database/repositories';
import { MessageNotificationService } from '../message-notification.service';
import { CommunityKafka } from '@app/shared/kafka/community.kafka';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AppLogger } from '@app/shared/logger/app.logger';
import { MESSAGE_STATUS, CONVERSATION_MEMBER_TYPE } from '@app/conversation/constants';

describe('ConversationMemberService - Optimized for Large Datasets', () => {
  let service: ConversationMemberService;
  let mockRepository: jest.Mocked<ConversationMemberRepositoryInterface>;

  beforeEach(async () => {
    const mockRepositoryProvider = {
      provide: 'ConversationMemberRepositoryInterface',
      useValue: {
        query: jest.fn(),
        createQueryBuilder: jest.fn(),
        find: jest.fn(),
        findOneBy: jest.fn(),
        save: jest.fn(),
        updateBy: jest.fn(),
        deleteBy: jest.fn(),
        existsBy: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConversationMemberService,
        mockRepositoryProvider,
        {
          provide: MessageNotificationService,
          useValue: {},
        },
        {
          provide: CommunityKafka,
          useValue: {},
        },
        {
          provide: EventEmitter2,
          useValue: {},
        },
        {
          provide: AppLogger,
          useValue: {
            debug: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            log: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ConversationMemberService>(ConversationMemberService);
    mockRepository = module.get('ConversationMemberRepositoryInterface');
  });

  describe('getMessageUnreadByConversation - Optimized for Large Datasets', () => {
    it('should return empty object for empty conversation IDs', async () => {
      const result = await service.getMessageUnreadByConversation([], 'user-id');
      expect(result).toEqual({});
      expect(mockRepository.query).not.toHaveBeenCalled();
    });

    it('should execute optimized raw SQL query with correct parameters', async () => {
      const conversationIds = ['conv-1', 'conv-2'];
      const userId = 'user-123';
      
      const mockQueryResult = [
        {
          conversation_id: 'conv-1',
          last_read_time: new Date('2024-01-01T10:00:00Z'),
          total: '5',
        },
        {
          conversation_id: 'conv-2',
          last_read_time: new Date('2024-01-01T11:00:00Z'),
          total: '3',
        },
      ];

      mockRepository.query.mockResolvedValue(mockQueryResult);

      const result = await service.getMessageUnreadByConversation(conversationIds, userId);

      expect(mockRepository.query).toHaveBeenCalledWith(
        expect.stringContaining('FROM conversation_members cm'),
        [
          conversationIds,
          userId,
          MESSAGE_STATUS.PUBLISHED,
          MESSAGE_STATUS.DRAFTED,
          CONVERSATION_MEMBER_TYPE.OWNER,
        ]
      );

      expect(result).toEqual({
        'conv-1': {
          total: 5,
          lastReadTime: new Date('2024-01-01T10:00:00Z'),
        },
        'conv-2': {
          total: 3,
          lastReadTime: new Date('2024-01-01T11:00:00Z'),
        },
      });
    });

    it('should verify optimized raw SQL query structure for large datasets', async () => {
      const conversationIds = ['conv-1'];
      const userId = 'user-123';
      
      mockRepository.query.mockResolvedValue([]);

      await service.getMessageUnreadByConversation(conversationIds, userId);

      const [query] = mockRepository.query.mock.calls[0];
      
      // Verify main query structure optimized for large datasets
      expect(query).toContain('FROM conversation_members cm');
      expect(query).toContain('LEFT JOIN (');
      expect(query).toContain('COUNT(m.id) as total');
      
      // Verify key conditions
      expect(query).toContain('cm.conversation_id = ANY($1::uuid[])');
      expect(query).toContain('cm.user_id = $2');
      expect(query).toContain('cm.deleted_at IS NULL');
      expect(query).toContain('m.deleted_at IS NULL');
      expect(query).toContain('cm_inner.last_read_time IS NULL OR m.created_at > cm_inner.last_read_time');
      
      // Verify status conditions
      expect(query).toContain('m.status = $3');
      expect(query).toContain('m.status = $4 AND cm_inner.is_owner = $5');
      
      // Verify ordering optimized for large datasets
      expect(query).toContain('ORDER BY cm.last_read_time DESC NULLS LAST');
      
      // Verify subquery optimization for performance
      expect(query).toContain('INNER JOIN conversation_members cm_inner');
      expect(query).toContain('GROUP BY m.conversation_id');
    });

    it('should handle performance optimization for large conversation lists', async () => {
      // Test with a large number of conversations (simulating real-world scenario)
      const conversationIds = Array.from({ length: 100 }, (_, i) => `conv-${i}`);
      const userId = 'user-123';
      
      const mockQueryResult = conversationIds.map((id, index) => ({
        conversation_id: id,
        last_read_time: new Date(`2024-01-01T${10 + index}:00:00Z`),
        total: String(index % 10), // Varying unread counts
      }));

      mockRepository.query.mockResolvedValue(mockQueryResult);

      const result = await service.getMessageUnreadByConversation(conversationIds, userId);

      // Verify single query execution (no batching needed for this optimization)
      expect(mockRepository.query).toHaveBeenCalledTimes(1);
      
      // Verify all conversations are processed
      expect(Object.keys(result)).toHaveLength(100);
      
      // Verify correct parameter passing for large arrays
      expect(mockRepository.query).toHaveBeenCalledWith(
        expect.any(String),
        [
          conversationIds,
          userId,
          MESSAGE_STATUS.PUBLISHED,
          MESSAGE_STATUS.DRAFTED,
          CONVERSATION_MEMBER_TYPE.OWNER,
        ]
      );
    });
  });
});
