import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsInt,
  IsOptional,
  IsUUID,
  Max,
  Min,
  ValidateIf,
} from 'class-validator';
import { isEmpty } from 'lodash';
import { MESSAGE_STATUS } from '../constants';

export class GetChatHistoryQueryDto {
  @ApiProperty({
    description: 'The maximum number of items to return (must be ≤ 50)',
    example: 50,
    type: Number,
    required: true,
  })
  @Transform(({ value }) => (value !== undefined ? parseInt(value, 10) : value))
  @IsInt()
  @Max(50, { message: 'Limit must be less than or equal to 50.' })
  limit: number;

  @ApiPropertyOptional({
    description:
      'Number of items to skip (only valid if before, after and around are not provided)',
    type: Number,
    example: 0,
  })
  @ValidateIf((dto) => !dto.before && !dto.after && !dto.around)
  @IsOptional()
  @Transform(({ value }) => (value !== undefined ? parseInt(value, 10) : value))
  @IsInt()
  @Min(0, { message: 'skip must be greater than or equal to 0' })
  skip?: number;

  @ValidateIf((dto) => !!dto.before)
  @ApiPropertyOptional({
    example: '',
    description: 'Fetch items before this ID (UUID)',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID()
  before?: string;

  @ValidateIf((dto) => !!dto.after)
  @ApiPropertyOptional({
    example: '',
    description: 'Fetch items after this ID (UUID)',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID()
  after?: string;

  @ValidateIf((dto) => !!dto.around)
  @ApiPropertyOptional({
    example: '',
    description: 'Fetch items around this ID (UUID)',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID()
  around?: string;

  @ValidateIf((dto) => isEmpty(dto.replyThread))
  @ApiPropertyOptional({
    example: '',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  replyThread?: boolean;

  @ValidateIf((dto) => !!dto.parentId)
  @ApiPropertyOptional({
    example: '',
    description: 'Fetch items has parent ID (UUID)',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID()
  parentId?: string;

  @ApiProperty({
    description: 'Filter status: 0-drafted; 1-scheduled; 2-published',
    example: MESSAGE_STATUS.PUBLISHED,
    type: Number,
    required: false,
  })
  @Transform(({ value }) => (value !== undefined ? parseInt(value) : value))
  @IsOptional()
  @IsInt()
  status?: number;

  @ValidateIf((dto) => isEmpty(dto.aroundFirstUnreadMessage))
  @ApiPropertyOptional({
    description: 'Fetch items around first unread message in conversation',
    example: '',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  aroundFirstUnreadMessage?: boolean;
}
